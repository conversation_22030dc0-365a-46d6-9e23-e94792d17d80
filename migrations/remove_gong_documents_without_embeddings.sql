-- Migration: Remove Gong documents that have no embeddings
-- Description: This migration removes Gong call transcript documents that have no embeddings,
--              which are likely failed processing attempts or duplicates.
-- Date: 2025-01-30
-- Author: System Migration

-- First, let's create a backup table to store information about documents we're about to delete
-- This allows for recovery if needed
CREATE TABLE IF NOT EXISTS migration_backup_gong_docs_no_embeddings (
    id INTEGER,
    file_name TEXT,
    label TEXT,
    created_date TIMESTAMP,
    metadata_json JSONB,
    document_type_id INTEGER,
    backup_timestamp TIMESTAMP DEFAULT NOW()
);

-- Insert backup data for Gong documents without embeddings
INSERT INTO migration_backup_gong_docs_no_embeddings (
    id, file_name, label, created_date, metadata_json, document_type_id
)
SELECT 
    d.id,
    d.file_name,
    d.label,
    d.created_date,
    d.metadata_json,
    d.document_type_id
FROM document d
JOIN document_type dt ON d.document_type_id = dt.id
WHERE d.status != 'deleted'
    AND d.use_for_generation = true
    AND dt.name = 'Call Transcript'
    AND d.metadata_json->>'externalDocSource' = 'gong'
    AND NOT EXISTS (
        SELECT 1 
        FROM embedding e 
        WHERE e.document_id = d.id
    );

-- Log the count of documents we're about to delete
DO $$
DECLARE
    doc_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO doc_count
    FROM document d
    JOIN document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM embedding e 
            WHERE e.document_id = d.id
        );
    
    RAISE NOTICE 'About to delete % Gong documents without embeddings', doc_count;
END $$;

-- Delete Gong documents that have no embeddings
-- We use a CTE to be explicit about what we're deleting
WITH gong_docs_to_delete AS (
    SELECT d.id
    FROM document d
    JOIN document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM embedding e 
            WHERE e.document_id = d.id
        )
)
DELETE FROM document 
WHERE id IN (SELECT id FROM gong_docs_to_delete);

-- Log the final count
DO $$
DECLARE
    remaining_count INTEGER;
    backup_count INTEGER;
BEGIN
    -- Count remaining Gong documents
    SELECT COUNT(*) INTO remaining_count
    FROM document d
    JOIN document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong';
    
    -- Count backed up documents
    SELECT COUNT(*) INTO backup_count
    FROM migration_backup_gong_docs_no_embeddings;
    
    RAISE NOTICE 'Migration completed. Remaining Gong documents: %. Backed up documents: %', 
                 remaining_count, backup_count;
END $$;

-- Create an index on the backup table for easier querying
CREATE INDEX IF NOT EXISTS idx_backup_gong_docs_metadata 
ON migration_backup_gong_docs_no_embeddings USING GIN (metadata_json);

-- Add a comment to document this migration
COMMENT ON TABLE migration_backup_gong_docs_no_embeddings IS 
'Backup table for Gong documents without embeddings that were removed in migration on 2025-01-30. 
These documents were likely failed processing attempts or duplicates.';
