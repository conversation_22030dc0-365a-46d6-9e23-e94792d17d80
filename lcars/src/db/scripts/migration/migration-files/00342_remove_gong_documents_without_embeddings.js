export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
DO $$
DECLARE
    gong_docs_without_embeddings_count INTEGER;
    deleted_documents_count INTEGER;
    remaining_gong_docs_count INTEGER;
    schema_name TEXT := '{schema}';
BEGIN
    -- Count Gong documents without embeddings before deletion
    SELECT COUNT(*) INTO gong_docs_without_embeddings_count
    FROM {schema}.document d
    JOIN {schema}.document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM {schema}.embedding e 
            WHERE e.document_id = d.id
        );
    
    -- If no Gong documents without embeddings found, skip cleanup
    IF gong_docs_without_embeddings_count = 0 THEN
        RAISE NOTICE 'No Gong documents without embeddings found in schema %. Skipping cleanup.', schema_name;
        RETURN;
    END IF;
    
    RAISE NOTICE 'Found % Gong documents without embeddings in schema %. Starting cleanup...', 
                 gong_docs_without_embeddings_count, schema_name;
    
    -- Create backup table for recovery if needed
    CREATE TABLE IF NOT EXISTS {schema}.migration_backup_gong_docs_no_embeddings (
        id INTEGER,
        file_name TEXT,
        label TEXT,
        created_date TIMESTAMP,
        metadata_json JSONB,
        document_type_id INTEGER,
        backup_timestamp TIMESTAMP DEFAULT NOW()
    );
    
    -- Insert backup data for Gong documents without embeddings
    INSERT INTO {schema}.migration_backup_gong_docs_no_embeddings (
        id, file_name, label, created_date, metadata_json, document_type_id
    )
    SELECT 
        d.id,
        d.file_name,
        d.label,
        d.created_date,
        d.metadata_json,
        d.document_type_id
    FROM {schema}.document d
    JOIN {schema}.document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong'
        AND NOT EXISTS (
            SELECT 1 
            FROM {schema}.embedding e 
            WHERE e.document_id = d.id
        );
    
    RAISE NOTICE 'Backed up % Gong documents without embeddings to migration_backup_gong_docs_no_embeddings table', 
                 gong_docs_without_embeddings_count;
    
    -- Delete Gong documents that have no embeddings
    -- These are likely failed processing attempts or duplicates
    WITH gong_docs_to_delete AS (
        SELECT d.id
        FROM {schema}.document d
        JOIN {schema}.document_type dt ON d.document_type_id = dt.id
        WHERE d.status != 'deleted'
            AND d.use_for_generation = true
            AND dt.name = 'Call Transcript'
            AND d.metadata_json->>'externalDocSource' = 'gong'
            AND NOT EXISTS (
                SELECT 1 
                FROM {schema}.embedding e 
                WHERE e.document_id = d.id
            )
    ),
    deleted_documents AS (
        DELETE FROM {schema}.document 
        WHERE id IN (SELECT id FROM gong_docs_to_delete)
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_documents_count FROM deleted_documents;
    
    -- Count remaining Gong documents after cleanup
    SELECT COUNT(*) INTO remaining_gong_docs_count
    FROM {schema}.document d
    JOIN {schema}.document_type dt ON d.document_type_id = dt.id
    WHERE d.status != 'deleted'
        AND d.use_for_generation = true
        AND dt.name = 'Call Transcript'
        AND d.metadata_json->>'externalDocSource' = 'gong';
    
    RAISE NOTICE 'Migration completed for schema %. Deleted % Gong documents without embeddings. Remaining Gong documents: %', 
                 schema_name, deleted_documents_count, remaining_gong_docs_count;
    
    -- Create index on backup table for easier querying
    CREATE INDEX IF NOT EXISTS idx_backup_gong_docs_metadata 
    ON {schema}.migration_backup_gong_docs_no_embeddings USING GIN (metadata_json);
    
END $$;
`;

export const down = `
DO $$
DECLARE
    backup_count INTEGER;
    restored_count INTEGER;
    schema_name TEXT := '{schema}';
BEGIN
    -- Check if backup table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = '{schema}' 
        AND table_name = 'migration_backup_gong_docs_no_embeddings'
    ) THEN
        RAISE NOTICE 'Backup table migration_backup_gong_docs_no_embeddings not found in schema %. Cannot restore.', schema_name;
        RETURN;
    END IF;
    
    -- Count backup records
    SELECT COUNT(*) INTO backup_count
    FROM {schema}.migration_backup_gong_docs_no_embeddings;
    
    IF backup_count = 0 THEN
        RAISE NOTICE 'No backup records found in schema %. Nothing to restore.', schema_name;
        RETURN;
    END IF;
    
    RAISE NOTICE 'Found % backup records in schema %. Starting restoration...', backup_count, schema_name;
    
    -- Restore documents from backup
    WITH restored_documents AS (
        INSERT INTO {schema}.document (
            id, file_name, label, created_date, metadata_json, document_type_id,
            status, use_for_generation
        )
        SELECT 
            b.id,
            b.file_name,
            b.label,
            b.created_date,
            b.metadata_json,
            b.document_type_id,
            'active' as status,
            true as use_for_generation
        FROM {schema}.migration_backup_gong_docs_no_embeddings b
        WHERE NOT EXISTS (
            SELECT 1 FROM {schema}.document d WHERE d.id = b.id
        )
        RETURNING id
    )
    SELECT COUNT(*) INTO restored_count FROM restored_documents;
    
    RAISE NOTICE 'Restoration completed for schema %. Restored % documents from backup.', 
                 schema_name, restored_count;
    
END $$;
`;
